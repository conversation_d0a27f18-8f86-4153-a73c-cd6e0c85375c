package event

import (
	"go_core_market/internal/crawler/domain/value_object"
	"time"
)

// ====== Event Types Constants ======
const (
	PricesCrawledEventType = "crawl.prices_crawled"
	ItemsCrawledEventType  = "crawl.items_crawled"
)

// ====== Prices Crawled Event ======
type PricesCrawledData struct {
	MarketID     int                            `json:"market_id"`
	MarketName   string                         `json:"market_name,omitempty"`
	CrawlType    string                         `json:"crawl_type"` // "normal", "doppler", "float", "fade"
	ItemID       int                            `json:"item_id,omitempty"`
	ItemName     string                         `json:"item_name,omitempty"`
	Prices       []*value_object.CrawledPrice   `json:"prices"`
	TotalPrices  int                            `json:"total_prices"`
	CrawledAt    time.Time                      `json:"crawled_at"`
	CrawledBy    string                         `json:"crawled_by,omitempty"`    // Optional: service/process that crawled
	RequestID    string                         `json:"request_id,omitempty"`    // Optional: trace request
	Page         int                            `json:"page,omitempty"`          // Optional: page number for normal crawl
	Conditions   []value_object.Condition       `json:"conditions,omitempty"`    // Optional: conditions used for crawl
	Duration     time.Duration                  `json:"duration,omitempty"`      // Optional: crawl duration
	Success      bool                           `json:"success"`                 // Whether crawl was successful
	ErrorMessage string                         `json:"error_message,omitempty"` // Optional: error message if failed
}

type PricesCrawledEvent struct {
	*BaseEvent[PricesCrawledData]
}

func NewPricesCrawledEvent(aggregateID string, data PricesCrawledData) *PricesCrawledEvent {
	return &PricesCrawledEvent{
		BaseEvent: NewBaseEvent(PricesCrawledEventType, aggregateID, data),
	}
}

// ====== Items Crawled Event ======
type ItemsCrawledData struct {
	MarketID     int                           `json:"market_id"`
	MarketName   string                        `json:"market_name,omitempty"`
	Items        []*value_object.CrawledItem   `json:"items"`
	TotalItems   int                           `json:"total_items"`
	CrawledAt    time.Time                     `json:"crawled_at"`
	CrawledBy    string                        `json:"crawled_by,omitempty"`    // Optional: service/process that crawled
	RequestID    string                        `json:"request_id,omitempty"`    // Optional: trace request
	Page         int                           `json:"page,omitempty"`          // Optional: page number
	Duration     time.Duration                 `json:"duration,omitempty"`      // Optional: crawl duration
	Success      bool                          `json:"success"`                 // Whether crawl was successful
	ErrorMessage string                        `json:"error_message,omitempty"` // Optional: error message if failed
}

type ItemsCrawledEvent struct {
	*BaseEvent[ItemsCrawledData]
}

func NewItemsCrawledEvent(aggregateID string, data ItemsCrawledData) *ItemsCrawledEvent {
	return &ItemsCrawledEvent{
		BaseEvent: NewBaseEvent(ItemsCrawledEventType, aggregateID, data),
	}
}
