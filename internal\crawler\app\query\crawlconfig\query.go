package querycrawlconfig

import (
	"go_core_market/pkg/decorator"
	"go_core_market/pkg/logger"
)

type CrawlConfigQueries struct {
	GetAllCrawlConfigs    GetAllCrawlConfigsQueryHandler
	FilterCrawlConfigs    FilterCrawlConfigsQueryHandler
	GetActiveCrawlConfigs GetActiveCrawlConfigsQueryHandler
}

func NewCrawlConfigQueries(
	repo CrawlConfigReadModel,
	logger logger.Logger,
	metricsClient decorator.MetricsClient) *CrawlConfigQueries {
	return &CrawlConfigQueries{
		GetAllCrawlConfigs:    NewGetAllCrawlConfigsQueryHandler(repo, logger, metricsClient),
		FilterCrawlConfigs:    NewFilterCrawlConfigsQueryHandler(repo, logger, metricsClient),
		GetActiveCrawlConfigs: NewGetActiveCrawlConfigsQueryHandler(repo, logger, metricsClient),
	}
}
