package service

import (
	"context"
	"go_core_market/internal/crawler/domain/entity"
	"go_core_market/pkg/message"
	"go_core_market/pkg/message/event"
	"go_core_market/pkg/message/kafka"
)

// EventService handles event publishing for crawler operations
type EventService struct {
	broker message.HybridBroker
}

func NewEventService() *EventService {
	// Setup internal event bus
	internalBus := message.NewMemoryEventBus()
	
	// Setup external Kafka broker
	kafkaConfig := kafka.Config{
		Brokers:  []string{"localhost:9092"},
		GroupID:  "crawler-service",
		// ... other kafka config
	}
	externalBus := kafka.NewKafkaBroker(kafkaConfig)
	
	// Setup hybrid broker with crawler-specific config
	hybridConfig := message.DefaultCrawlerHybridConfig()
	hybridBroker := message.NewHybridBroker(internalBus, externalBus, hybridConfig)
	
	return &EventService{
		broker: *hybridBroker,
	}
}

// Job Events
func (s *EventService) PublishJobCreated(ctx context.Context, job *entity.Job) error {
	event := event.CreateJobCreatedEventFromDomain(job)
	return s.broker.Publish(ctx, event)
}

func (s *EventService) PublishJobStarted(ctx context.Context, job *entity.Job, startedBy string) error {
	event := event.CreateJobStartedEventFromDomain(job, startedBy)
	return s.broker.Publish(ctx, event)
}

func (s *EventService) PublishJobProgress(ctx context.Context, job *entity.Job) error {
	event := event.CreateJobProgressUpdatedEventFromDomain(job)
	return s.broker.Publish(ctx, event)
}

func (s *EventService) PublishJobCompleted(ctx context.Context, job *entity.Job, results map[string]interface{}) error {
	event := event.CreateJobCompletedEventFromDomain(job, results)
	return s.broker.Publish(ctx, event)
}

func (s *EventService) PublishJobFailed(ctx context.Context, job *entity.Job) error {
	event := event.CreateJobFailedEventFromDomain(job)
	return s.broker.Publish(ctx, event)
}

// Crawl Events
func (s *EventService) PublishPricesCrawled(ctx context.Context, data event.PricesCrawledData) error {
	aggregateID := "market-" + string(rune(data.MarketID))
	event := event.NewPricesCrawledEvent(aggregateID, data)
	return s.broker.Publish(ctx, event)
}

func (s *EventService) PublishItemsCrawled(ctx context.Context, data event.ItemsCrawledData) error {
	aggregateID := "market-" + string(rune(data.MarketID))
	event := event.NewItemsCrawledEvent(aggregateID, data)
	return s.broker.Publish(ctx, event)
}

// Subscribe to internal events for real-time updates
func (s *EventService) SetupInternalSubscriptions(ctx context.Context) error {
	// Subscribe to job progress for real-time UI updates
	err := s.broker.SubscribeLocal("job.progress_updated", s.handleJobProgressUpdate)
	if err != nil {
		return err
	}
	
	// Subscribe to crawl status changes
	err = s.broker.SubscribeLocal("crawl.status_changed", s.handleCrawlStatusChange)
	if err != nil {
		return err
	}
	
	return nil
}

// Event handlers
func (s *EventService) handleJobProgressUpdate(ctx context.Context, event event.Event) error {
	// Handle real-time job progress updates
	// e.g., update WebSocket connections, update cache, etc.
	return nil
}

func (s *EventService) handleCrawlStatusChange(ctx context.Context, event event.Event) error {
	// Handle crawl status changes
	// e.g., update internal state, trigger dependent operations
	return nil
}

// Example usage in Application Service
type CrawlerApplicationService struct {
	eventService *EventService
	// ... other dependencies
}

func (s *CrawlerApplicationService) StartCrawlJob(ctx context.Context, jobID int) error {
	// 1. Get job from repository
	// job, err := s.jobRepo.GetByID(ctx, jobID)
	
	// 2. Start the job
	// err = job.Start()
	
	// 3. Publish job started event (goes to both internal and external)
	// err = s.eventService.PublishJobStarted(ctx, job, "system")
	
	// 4. Start actual crawling process
	// go s.performCrawling(ctx, job)
	
	return nil
}

func (s *CrawlerApplicationService) performCrawling(ctx context.Context, job *entity.Job) {
	// Simulate crawling process with progress updates
	
	// Update progress (internal only - fast UI updates)
	// progress := &entity.JobProgress{CurrentStep: 1, TotalSteps: 10, Message: "Starting crawl", Percentage: 10}
	// job.UpdateProgress(progress)
	// s.eventService.PublishJobProgress(ctx, job)
	
	// ... perform actual crawling ...
	
	// Publish crawl results (external - for analytics)
	// s.eventService.PublishPricesCrawled(ctx, crawlData)
	
	// Complete job (broadcast - both internal and external)
	// job.Complete()
	// s.eventService.PublishJobCompleted(ctx, job, results)
}
